package me.darkness.turbodrop.utils;

public class TimeUtils {
    
    public static String formatTime(long seconds) {
        long days = seconds / 86400;
        seconds = seconds % 86400;
        long hours = seconds / 3600;
        seconds = seconds % 3600;
        long minutes = seconds / 60;
        
        StringBuilder result = new StringBuilder();
        
        if (days > 0) {
            result.append(days).append("d ");
        }
        if (hours > 0) {
            result.append(hours).append("h ");
        }
        if (minutes > 0) {
            result.append(minutes).append("min");
        }
        
        return result.toString().trim();
    }
    
    public static String formatExactTime(long seconds) {
        long days = seconds / 86400;
        seconds = seconds % 86400;
        long hours = seconds / 3600;
        seconds = seconds % 3600;
        long minutes = seconds / 60;
        
        StringBuilder result = new StringBuilder();
        
        if (days > 0) {
            result.append(days).append(" dni ");
        }
        if (hours > 0) {
            result.append(hours).append(" godzin ");
        }
        if (minutes > 0) {
            result.append(minutes).append(" minut");
        }
        
        return result.toString().trim();
    }
}