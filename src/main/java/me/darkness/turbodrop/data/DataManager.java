package me.darkness.turbodrop.data;

import me.darkness.turbodrop.Main;
import me.darkness.turbodrop.utils.NumberUtils;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class DataManager {
    
    private final Main plugin;
    private File dataFile;
    private FileConfiguration dataConfig;
    
    public DataManager(Main plugin) {
        this.plugin = plugin;
        setupDataFile();
        loadData();
    }
    
    private void setupDataFile() {
        dataFile = new File(plugin.getDataFolder(), "data.yml");
        if (!dataFile.exists()) {
            try {
                dataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("Could not create data.yml file!");
                e.printStackTrace();
            }
        }
        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
    }
    
    public void loadData() {
        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
    }
    
    public void saveData() {
        try {
            dataConfig.save(dataFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Could not save data.yml file!");
            e.printStackTrace();
        }
    }
    
    public double getCollectedAmount() {
        return dataConfig.getDouble("turbodrop.collected", 0.0);
    }
    
    public void setCollectedAmount(double amount) {
        dataConfig.set("turbodrop.collected", amount);
    }
    
    public boolean isActive() {
        return dataConfig.getBoolean("turbodrop.active", false);
    }
    
    public void setActive(boolean active) {
        dataConfig.set("turbodrop.active", active);
    }
    
    public long getEndTime() {
        return dataConfig.getLong("turbodrop.end-time", 0);
    }
    
    public void setEndTime(long endTime) {
        dataConfig.set("turbodrop.end-time", endTime);
    }
    
    public double getTargetPrice() {
        return dataConfig.getDouble("turbodrop.target-price", plugin.getConfigManager().getDefaultPrice());
    }
    
    public void setTargetPrice(double price) {
        dataConfig.set("turbodrop.target-price", price);
    }
    
    public List<String> getPaymentHistory() {
        return dataConfig.getStringList("turbodrop.history");
    }
    
    public void setPaymentHistory(List<String> history) {
        dataConfig.set("turbodrop.history", history);
    }
    
    public void addPaymentToHistory(String playerName, double amount) {
        List<String> history = getPaymentHistory();
        String entry = "&8• &e" + playerName + " &8» &a$" + NumberUtils.formatNumber(amount);
        
        history.add(entry);
        
        int maxSize = plugin.getConfigManager().getHistorySize();
        while (history.size() > maxSize) {
            history.remove(0);
        }
        
        setPaymentHistory(history);
        saveData();
    }
    
    public void clearPaymentHistory() {
        setPaymentHistory(new ArrayList<>());
    }
    
    public void resetData() {
        setCollectedAmount(0.0);
        setActive(false);
        setEndTime(0);
        setPaymentHistory(new ArrayList<>());
        saveData();
    }
}