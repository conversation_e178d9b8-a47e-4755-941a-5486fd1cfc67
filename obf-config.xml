<?xml version="1.0" encoding="UTF-8"?>
<config>
    <input>
        <jar in="target/777-Economy-1.1.jar" out="target/777-Economy-1.1-obf.jar"/>
    </input>

    <keep-names>
        <class access="public" name="me.darkness.turbodrop.Main">
            <method access="public" name="onEnable"/>
            <method access="public" name="onDisable"/>
            <method access="public" name="onLoad"/>
            <method access="public" name="getInstance"/>
        </class>

        <class template="class me.darkness.turbodrop.commands.**">
            <method access="public" name="onCommand"/>
            <method access="public" name="onTabComplete"/>
        </class>

        <class template="class me.darkness.turbodrop.listeners.**">
            <method access="public" name="*Event*"/>
        </class>

        <!-- Keep config manager methods -->
        <class template="class me.darkness.turbodrop.config.**">
            <method access="public" name="get*"/>
            <method access="public" name="set*"/>
            <method access="public" name="load*"/>
            <method access="public" name="save*"/>
            <method access="public" name="reload*"/>
        </class>

        <!-- Keep data manager methods -->
        <class template="class me.darkness.turbodrop.data.**">
            <method access="public" name="get*"/>
            <method access="public" name="set*"/>
            <method access="public" name="load*"/>
            <method access="public" name="save*"/>
            <method access="public" name="is*"/>
            <method access="public" name="add*"/>
            <method access="public" name="clear*"/>
        </class>

        <!-- Keep economy manager methods -->
        <class template="class me.darkness.turbodrop.economy.**">
            <method access="public" name="setup*"/>
            <method access="public" name="get*"/>
            <method access="public" name="has*"/>
            <method access="public" name="withdraw*"/>
            <method access="public" name="deposit*"/>
            <method access="public" name="format"/>
        </class>

        <!-- Keep GUI manager methods -->
        <class template="class me.darkness.turbodrop.gui.**">
            <method access="public" name="open*"/>
            <method access="public" name="create*"/>
            <method access="public" name="update*"/>
        </class>

        <!-- Keep manager classes -->
        <class template="class me.darkness.turbodrop.managers.**">
            <method access="public" name="*"/>
        </class>

        <!-- Keep utility classes -->
        <class template="class me.darkness.turbodrop.utils.**">
            <method access="public" name="*"/>
        </class>
    </keep-names>

    <ignore-classes>
        <class template="class org.bukkit.**"/>
        <class template="class org.spigotmc.**"/>
        <class template="class net.md_5.**"/>
        <class template="class com.github.milkbowl.vault.**"/>
        <class template="class net.milkbowl.vault.**"/>
        <class template="class org.yaml.**"/>
        <class template="class javax.**"/>
        <class template="class java.**"/>
        <class template="class sun.**"/>
        <class template="class com.google.**"/>
        <class template="class org.apache.**"/>
        <class template="class org.slf4j.**"/>
    </ignore-classes>

    <watermark key="777" value="777CODE"/>
    <property name="log-file" value="target/obfuscation.log"/>
    <property name="random-seed" value="maximum"/>
    <property name="string-encryption" value="enable"/>
    <property name="control-flow-obfuscation" value="enable"/>
    <property name="extensive-flow-obfuscation" value="maximum"/>
    <property name="packages-naming" value="123"/>
    <property name="methods-naming" value="iii"/>
    <property name="fields-naming" value="iii"/>
    <property name="local-variables-naming" value="optimize"/>
    <property name="classes-naming" value="iii"/>
    <property name="line-numbers" value="obfuscate"/>
    <property name="generics" value="remove"/>
    <property name="inner-classes" value="remove"/>
    <property name="member-reorder" value="enable"/>
</config>
