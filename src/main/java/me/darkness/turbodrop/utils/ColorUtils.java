package me.darkness.turbodrop.utils;

import net.md_5.bungee.api.ChatColor;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ColorUtils {
    
    private static final Pattern HEX_PATTERN = Pattern.compile("&#([A-Fa-f0-9]{6})");
    
    public static String colorize(String message) {
        if (message == null) return null;
        
        Matcher matcher = HEX_PATTERN.matcher(message);
        while (matcher.find()) {
            String color = matcher.group(1);
            message = message.replace("&#" + color, ChatColor.of("#" + color) + "");
        }
        
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    

}