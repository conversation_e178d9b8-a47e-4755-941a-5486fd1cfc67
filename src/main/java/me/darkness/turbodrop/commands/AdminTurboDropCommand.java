package me.darkness.turbodrop.commands;

import me.darkness.turbodrop.Main;
import me.darkness.turbodrop.utils.NumberUtils;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class AdminTurboDropCommand implements CommandExecutor {
    
    private final Main plugin;
    
    public AdminTurboDropCommand(Main plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("turbodrop.admin")) {
            sender.sendMessage(plugin.getConfigManager().getMessage("no-permission"));
            return true;
        }
        
        if (args.length == 0) {
            sender.sendMessage(plugin.getConfigManager().getMessage("admin-invalid-usage"));
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "ustaw":
                handleSetPrice(sender, args);
                break;
            case "wlacz":
                handleEnable(sender);
                break;
            case "wylacz":
                handleDisable(sender);
                break;
            case "reload":
                handleReload(sender);
                break;
            default:
                sender.sendMessage(plugin.getConfigManager().getMessage("admin-invalid-usage"));
                break;
        }
        
        return true;
    }
    
    private void handleSetPrice(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(plugin.getConfigManager().getMessage("admin-invalid-usage"));
            return;
        }
        
        try {
            double price = Double.parseDouble(args[1]);
            if (price <= 0) {
                sender.sendMessage(plugin.getConfigManager().getMessage("invalid-price"));
                return;
            }
            
            plugin.getTurboDropManager().setTargetPrice(price);
            
            String message = plugin.getConfigManager().getMessage("admin-price-set");
            message = message.replace("%price%", NumberUtils.formatNumber(price));
            sender.sendMessage(message);
            
        } catch (NumberFormatException e) {
            sender.sendMessage(plugin.getConfigManager().getMessage("invalid-price"));
        }
    }
    
    private void handleEnable(CommandSender sender) {
        if (plugin.getDataManager().isActive()) {
            sender.sendMessage(plugin.getConfigManager().getMessage("turbodrop-already-active"));
            return;
        }
        
        plugin.getTurboDropManager().activateTurboDrop();
        sender.sendMessage(plugin.getConfigManager().getMessage("turbodrop-activated-manually"));
    }
    
    private void handleDisable(CommandSender sender) {
        if (!plugin.getDataManager().isActive()) {
            sender.sendMessage(plugin.getConfigManager().getMessage("turbodrop-not-active"));
            return;
        }
        
        plugin.getTurboDropManager().forceDisable();
        sender.sendMessage(plugin.getConfigManager().getMessage("turbodrop-disabled-success"));
    }
    
    private void handleReload(CommandSender sender) {
        try {
            // Przeładuj konfigurację
            plugin.getConfigManager().reloadConfig();
            
            // Wyślij wiadomość o sukcesie
            sender.sendMessage(plugin.getConfigManager().getMessage("config-reloaded"));
            
        } catch (Exception e) {
            String errorMessage = plugin.getConfigManager().getMessage("config-reload-error");
            errorMessage = errorMessage.replace("%error%", e.getMessage());
            sender.sendMessage(errorMessage);
            
            plugin.getLogger().severe("Błąd podczas przeładowywania konfiguracji: " + e.getMessage());
            e.printStackTrace();
        }
    }
}