package me.darkness.turbodrop.listeners;

import me.darkness.turbodrop.Main;
import me.darkness.turbodrop.utils.NumberUtils;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;

import java.util.List;
import java.util.Random;

public class BlockBreakListener implements Listener {
    
    private final Main plugin;
    private final Random random = new Random();
    
    public BlockBreakListener(Main plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Block block = event.getBlock();
        Material blockType = block.getType();
        Player player = event.getPlayer();

        double basePrice = plugin.getConfigManager().getBlockPrice(blockType);
        if (basePrice <= 0) {
            return;
        }

        // Zawsze wyłączamy naturalne dropy dla bloków z cenami, aby uniknąć podwójnego zarabiania
        // Gracz dostaje pieniądze zamiast itemów
        event.setDropItems(false);

        ItemStack tool = player.getInventory().getItemInMainHand();
        int fortuneLevel = tool.getEnchantmentLevel(Enchantment.LOOT_BONUS_BLOCKS);
        int fortuneMultiplier = calculateFortuneMultiplier(fortuneLevel, blockType);

        double finalAmount = basePrice * fortuneMultiplier;

        if (plugin.getDataManager().isActive()) {
            finalAmount *= 2;
        }

        if (plugin.getEconomyManager().depositPlayer(player, finalAmount)) {
            plugin.getActionBarManager().addEarnings(player, finalAmount);
        }
    }
    
    private int calculateFortuneMultiplier(int fortuneLevel, Material blockType) {
        if (fortuneLevel <= 0) {
            return 1;
        }
        if (fortuneLevel <= 3) {
            switch (fortuneLevel) {
                case 1:
                    return random.nextBoolean() ? 2 : 1;
                case 2:
                    int rand2 = random.nextInt(3);
                    return rand2 == 0 ? 1 : (rand2 == 1 ? 2 : 3);
                case 3:
                    return random.nextInt(4) + 1;
            }
        }
        int maxMultiplier = Math.min(fortuneLevel + 1, 50);
        return random.nextInt(maxMultiplier) + 1;
    }
}