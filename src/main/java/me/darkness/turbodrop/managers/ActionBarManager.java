package me.darkness.turbodrop.managers;

import me.darkness.turbodrop.Main;
import me.darkness.turbodrop.utils.NumberUtils;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class ActionBarManager {
    
    private final Main plugin;

    private final Map<UUID, Double> lastEarnings = new HashMap<>();
    private final Map<UUID, Long> lastMineTime = new HashMap<>();
    private final Map<UUID, Long> firstMineTime = new HashMap<>();
    
    public ActionBarManager(Main plugin) {
        this.plugin = plugin;
        startActionBarTasks();
    }
    
    private void startActionBarTasks() {
        if (!plugin.getConfigManager().isActionBarEnabled()) {
            return;
        }

        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    UUID playerId = player.getUniqueId();
                    boolean shouldShow = false;

                    if (lastMineTime.containsKey(playerId)) {
                        long now = System.currentTimeMillis();
                        long lastMine = lastMineTime.get(playerId);
                        long showDuration = plugin.getConfigManager().getActionBarShowDuration();
                        if (now - lastMine < showDuration) {
                            shouldShow = true;
                        }
                    }
                    
                    if (shouldShow) {
                        double amount = lastEarnings.getOrDefault(playerId, 0.0);
                        double minuteProjection = calculateMinuteProjection(playerId);
                        
                        String message = plugin.getConfigManager().getActionBarMessage("mining")
                                .replace("%amount%", NumberUtils.formatDecimal(amount))
                                .replace("%minute%", NumberUtils.formatDecimal(minuteProjection));
                        
                        sendActionBar(player, message);
                    } else {
                        lastEarnings.remove(playerId);
                        lastMineTime.remove(playerId);
                        firstMineTime.remove(playerId);
                        sendActionBar(player, "");
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, plugin.getConfigManager().getActionBarUpdateInterval());
    }
    
    public void addEarnings(Player player, double amount) {
        UUID playerId = player.getUniqueId();

        lastEarnings.put(playerId, lastEarnings.getOrDefault(playerId, 0.0) + amount);

        long currentTime = System.currentTimeMillis();
        lastMineTime.put(playerId, currentTime);
        
        // Zapisz czas pierwszego kopania jeśli nie istnieje
        if (!firstMineTime.containsKey(playerId)) {
            firstMineTime.put(playerId, currentTime);
        }
    }
    
    private void sendActionBar(Player player, String message) {
        if (message.isEmpty()) {
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR, new TextComponent(""));
        } else {
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR, new TextComponent(message));
        }
    }
    
    private double calculateMinuteProjection(UUID playerId) {
        if (!firstMineTime.containsKey(playerId) || !lastEarnings.containsKey(playerId)) {
            return 0.0;
        }
        
        long currentTime = System.currentTimeMillis();
        long firstMine = firstMineTime.get(playerId);
        double totalEarnings = lastEarnings.get(playerId);
        
        // Oblicz czas kopania w sekundach
        long miningTimeMs = currentTime - firstMine;
        if (miningTimeMs <= 0) {
            return 0.0;
        }
        
        // Oblicz zarobki na sekundę, potem na minutę
        double earningsPerSecond = totalEarnings / (miningTimeMs / 1000.0);
        return earningsPerSecond * 60.0; // 60 sekund = 1 minuta
    }
    
    public void clearPlayerData(UUID playerId) {
        lastEarnings.remove(playerId);
        lastMineTime.remove(playerId);
        firstMineTime.remove(playerId);
    }
}