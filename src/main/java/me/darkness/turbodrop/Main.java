package me.darkness.turbodrop;

import me.darkness.turbodrop.commands.AdminTurboDropCommand;
import me.darkness.turbodrop.commands.AdminTurboDropTabCompleter;
import me.darkness.turbodrop.commands.TurboDropCommand;
import me.darkness.turbodrop.commands.TurboDropTabCompleter;
import me.darkness.turbodrop.config.ConfigManager;
import me.darkness.turbodrop.data.DataManager;
import me.darkness.turbodrop.economy.EconomyManager;
import me.darkness.turbodrop.gui.GuiManager;
import me.darkness.turbodrop.listeners.BlockBreakListener;
import me.darkness.turbodrop.listeners.ChatListener;
import me.darkness.turbodrop.listeners.InventoryListener;
import me.darkness.turbodrop.listeners.PlayerListener;
import me.darkness.turbodrop.managers.ActionBarManager;
import me.darkness.turbodrop.managers.TurboDropManager;
import org.bukkit.plugin.java.JavaPlugin;

public final class Main extends JavaPlugin {

    private static Main instance;
    private ConfigManager configManager;
    private DataManager dataManager;
    private EconomyManager economyManager;
    private TurboDropManager turboDropManager;
    private GuiManager guiManager;
    private ActionBarManager actionBarManager;

    @Override
    public void onEnable() {
        instance = this;

        configManager = new ConfigManager(this);
        dataManager = new DataManager(this);

        getServer().getScheduler().runTask(this, () -> {
            economyManager = new EconomyManager(this);

            if (!economyManager.setupEconomy()) {
                getServer().getConsoleSender().sendMessage("§8[§4§l777-Economy§8] §cNie znaleziono pluginu §4§nVault§r§c. Ponawiam próbe znalezienia...");

                getServer().getScheduler().runTaskLater(this, () -> {
                    if (!economyManager.setupEconomy()) {
                        getServer().getConsoleSender().sendMessage("§8[§4§l777-Economy§8] §4§nVault§r§c nie znaleziony :C Wylaczam plugin...");
                        getServer().getPluginManager().disablePlugin(this);
                        return;
                    }

                    finishInitialization();
                }, 100L);
                return;
            }

            finishInitialization();
        });
    }

    private void finishInitialization() {
        turboDropManager = new TurboDropManager(this);
        guiManager = new GuiManager(this);
        actionBarManager = new ActionBarManager(this);

        getServer().getConsoleSender().sendMessage("§8[§6§l777-Economy§8] §f§nZaladowano konfiguracje§r.");
        registerCommands();
        getServer().getConsoleSender().sendMessage("§8[§6§l777-Economy§8] §f§nZarejestrowano komendy§r.");
        registerListeners();
        getServer().getConsoleSender().sendMessage("§8[§6§l777-Economy§8] §f§nZarejestrowano listenery§r.");

        getServer().getConsoleSender().sendMessage("§8[§6§l777-Economy§8] §aPomyslnie uruchomiono plugin!");
    }

    @Override
    public void onDisable() {
        if (turboDropManager != null) {
            turboDropManager.shutdown();
        }
        if (dataManager != null) {
            dataManager.saveData();
        }
        getServer().getConsoleSender().sendMessage("§8[§4§l777-Economy§8] §cWylaczono plugin :C");
    }

    private void registerCommands() {
        getCommand("turbodrop").setExecutor(new TurboDropCommand(this));
        getCommand("turbodrop").setTabCompleter(new TurboDropTabCompleter());

        getCommand("adminturbodrop").setExecutor(new AdminTurboDropCommand(this));
        getCommand("adminturbodrop").setTabCompleter(new AdminTurboDropTabCompleter());
    }

    private void registerListeners() {
        getServer().getPluginManager().registerEvents(new BlockBreakListener(this), this);
        getServer().getPluginManager().registerEvents(new InventoryListener(this), this);
        getServer().getPluginManager().registerEvents(new ChatListener(this), this);
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public DataManager getDataManager() {
        return dataManager;
    }

    public EconomyManager getEconomyManager() {
        return economyManager;
    }

    public TurboDropManager getTurboDropManager() {
        return turboDropManager;
    }

    public GuiManager getGuiManager() {
        return guiManager;
    }

    public ActionBarManager getActionBarManager() {
        return actionBarManager;
    }
}
