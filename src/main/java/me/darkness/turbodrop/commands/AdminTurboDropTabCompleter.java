package me.darkness.turbodrop.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class AdminTurboDropTabCompleter implements TabCompleter {
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (!sender.hasPermission("turbodrop.admin")) {
            return completions;
        }
        
        if (args.length == 1) {
            List<String> options = Arrays.asList("ustaw", "wlacz", "wylacz", "reload");
            for (String option : options) {
                if (option.toLowerCase().startsWith(args[0].toLowerCase())) {
                    completions.add(option);
                }
            }
        } else if (args.length == 2 && args[0].equalsIgnoreCase("ustaw")) {
            List<String> prices = Arrays.asList("500000", "1000000", "1500000", "2000000", "5000000");
            for (String price : prices) {
                if (price.startsWith(args[1])) {
                    completions.add(price);
                }
            }
        }
        
        return completions;
    }
}