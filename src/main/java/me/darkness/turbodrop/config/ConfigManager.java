package me.darkness.turbodrop.config;

import me.darkness.turbodrop.Main;
import me.darkness.turbodrop.utils.ColorUtils;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.List;
import java.util.stream.Collectors;

public class ConfigManager {
    
    private final Main plugin;
    private FileConfiguration config;
    
    public ConfigManager(Main plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    public void loadConfig() {
        plugin.saveDefaultConfig();
        plugin.reloadConfig();
        config = plugin.getConfig();
    }
    
    public void reloadConfig() {
        loadConfig();
    }
    
    public String getPrefix() {
        String prefix = config.getString("messages.prefix", "&#EF7400ᴛ&#F07A04ᴜ&#F07F07ʀ&#F1850Bʙ&#F18B0Fᴏ&#F29012ᴅ&#F29616ʀ&#F39B19ᴏ&#F3A11Dᴘ &8»");
        return ColorUtils.colorize(prefix);
    }
    
    public String colorize(String text) {
        return ColorUtils.colorize(text);
    }
    
    public String getMessage(String path) {
        String message = config.getString("messages." + path, "Message not found: " + path);
        message = message.replace("%prefix%", getPrefix());
        return ColorUtils.colorize(message);
    }
    
    public List<String> getMessageList(String path) {
        List<String> messages = config.getStringList("messages." + path);
        return messages.stream()
                .map(msg -> msg.replace("%prefix%", getPrefix()))
                .map(ColorUtils::colorize)
                .collect(Collectors.toList());
    }
    
    public String getGuiTitle(String guiType) {
        String title = config.getString("gui." + guiType + ".title", "GUI");
        return ColorUtils.colorize(title);
    }
    
    public int getGuiSize(String guiType) {
        return config.getInt("gui." + guiType + ".size", 54);
    }
    
    public String getGuiItemName(String guiType, String item) {
        String name = config.getString("gui." + guiType + ".items." + item + ".name", "Item");
        return ColorUtils.colorize(name);
    }
    
    public String getGuiItemName(String guiType, String item, String variant) {
        String name = config.getString("gui." + guiType + ".items." + item + ".name-" + variant, "Item");
        return ColorUtils.colorize(name);
    }
    
    public List<String> getGuiItemLore(String guiType, String item) {
        List<String> lore = config.getStringList("gui." + guiType + ".items." + item + ".lore");
        return lore.stream()
                .map(ColorUtils::colorize)
                .collect(Collectors.toList());
    }
    
    public List<String> getGuiItemLore(String guiType, String item, String variant) {
        List<String> lore = config.getStringList("gui." + guiType + ".items." + item + ".lore-" + variant);
        return lore.stream()
                .map(ColorUtils::colorize)
                .collect(Collectors.toList());
    }
    
    public Material getGuiItemMaterial(String guiType, String item) {
        String materialName = config.getString("gui." + guiType + ".items." + item + ".material", "STONE");
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid material: " + materialName + " for " + guiType + "." + item);
            return Material.STONE;
        }
    }
    
    public Material getGuiItemMaterial(String guiType, String item, String variant) {
        String materialName = config.getString("gui." + guiType + ".items." + item + ".material-" + variant, "STONE");
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid material: " + materialName + " for " + guiType + "." + item + "-" + variant);
            return Material.STONE;
        }
    }
    
    public int getGuiItemSlot(String guiType, String item) {
        return config.getInt("gui." + guiType + ".items." + item + ".slot", 0);
    }
    
    public List<Integer> getGuiItemSlots(String guiType, String item) {
        return config.getIntegerList("gui." + guiType + ".items." + item + ".slots");
    }
    
    public double getDefaultPrice() {
        return config.getDouble("settings.default-price", 1000000);
    }
    
    public int getDurationMinutes() {
        return config.getInt("settings.duration-minutes", 60);
    }
    
    public int getHistorySize() {
        return config.getInt("settings.history-size", 10);
    }
    

    
    public List<Material> getAffectedBlocks() {
        return config.getConfigurationSection("block-prices").getKeys(false).stream()
                .map(name -> {
                    try {
                        return Material.valueOf(name);
                    } catch (IllegalArgumentException e) {
                        plugin.getLogger().warning("Invalid material in block-prices: " + name);
                        return null;
                    }
                })
                .filter(material -> material != null)
                .collect(Collectors.toList());
    }
    
    public double getBlockPrice(Material material) {
        return config.getDouble("block-prices." + material.name(), 0.0);
    }
    
    public int getProgressBarLength() {
        return config.getInt("progress-bar.length", 20);
    }
    
    public String getProgressBarFilledChar() {
        String filled = config.getString("progress-bar.filled-char", "&a|");
        return ChatColor.translateAlternateColorCodes('&', filled);
    }
    
    public String getProgressBarEmptyChar() {
        String empty = config.getString("progress-bar.empty-char", "&7|");
        return ChatColor.translateAlternateColorCodes('&', empty);
    }
    
    public String getProgressBarFormat() {
        String format = config.getString("progress-bar.format", "&8[%bar%&8]");
        return ColorUtils.colorize(format);
    }

    public boolean isActionBarEnabled() {
        return config.getBoolean("actionbar.enabled", true);
    }
    
    public String getActionBarMessage(String messageType) {
        String message = config.getString("actionbar.messages." + messageType, "");
        return ColorUtils.colorize(message);
    }
    
    public long getActionBarShowDuration() {
        return 4000L;
    }
    
    public long getActionBarUpdateInterval() {
        return 40L;
    }
    


    public String getStatusText(String guiType, String item, String variant) {
        String status = config.getString("gui." + guiType + ".items." + item + ".status-" + variant, "&7Status");
        return ColorUtils.colorize(status);
    }
}