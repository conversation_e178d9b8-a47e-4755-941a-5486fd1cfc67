package me.darkness.turbodrop.managers;

import me.darkness.turbodrop.Main;
import me.darkness.turbodrop.utils.TimeUtils;
import me.darkness.turbodrop.utils.NumberUtils;
import org.bukkit.Bukkit;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.List;

public class TurboDropManager {
    
    private final Main plugin;
    private BukkitTask countdownTask;
    private BukkitTask bossBarTask;
    private BossBar bossBar;
    private int animationFrame = 0;
    
    public TurboDropManager(Main plugin) {
        this.plugin = plugin;
        startAnimationTask();
        
        if (plugin.getDataManager().isActive()) {
            long endTime = plugin.getDataManager().getEndTime();
            if (System.currentTimeMillis() < endTime) {
                startCountdown(endTime);
                showBossBar();
            } else {
                deactivateTurboDrop();
            }
        }
    }
    
    private void startAnimationTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                animationFrame++;
                if (animationFrame > 5) {
                    animationFrame = 0;
                }
            }
        }.runTaskTimer(plugin, 0L, 10L);
    }
    
    public void activateTurboDrop() {
        if (plugin.getDataManager().isActive()) {
            return;
        }
        
        long duration = plugin.getConfigManager().getDurationMinutes() * 60 * 1000L;
        long endTime = System.currentTimeMillis() + duration;
        
        plugin.getDataManager().setActive(true);
        plugin.getDataManager().setEndTime(endTime);
        plugin.getDataManager().clearPaymentHistory();
        plugin.getDataManager().saveData();
        
        broadcastActivation();
        startCountdown(endTime);
        showBossBar();
    }
    
    public void deactivateTurboDrop() {
        if (!plugin.getDataManager().isActive()) {
            return;
        }
        
        double collected = plugin.getDataManager().getCollectedAmount();
        
        plugin.getDataManager().setActive(false);
        plugin.getDataManager().setEndTime(0);
        plugin.getDataManager().setCollectedAmount(0.0);
        plugin.getDataManager().clearPaymentHistory();
        plugin.getDataManager().saveData();
        
        if (countdownTask != null) {
            countdownTask.cancel();
            countdownTask = null;
        }
        
        hideBossBar();
        broadcastDeactivation(collected);
    }
    
    public void forceDisable() {
        if (!plugin.getDataManager().isActive()) {
            return;
        }
        
        plugin.getDataManager().setActive(false);
        plugin.getDataManager().setEndTime(0);
        plugin.getDataManager().setCollectedAmount(0.0);
        plugin.getDataManager().clearPaymentHistory();
        plugin.getDataManager().saveData();
        
        if (countdownTask != null) {
            countdownTask.cancel();
            countdownTask = null;
        }
        
        hideBossBar();
        broadcastDisabled();
    }
    
    private void startCountdown(long endTime) {
        if (countdownTask != null) {
            countdownTask.cancel();
        }
        
        countdownTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (System.currentTimeMillis() >= endTime) {
                    deactivateTurboDrop();
                    cancel();
                }
            }
        }.runTaskTimer(plugin, 0L, 20L);
    }
    
    public boolean makePayment(Player player, double amount) {
        if (plugin.getDataManager().isActive()) {
            return false;
        }
        
        double collected = plugin.getDataManager().getCollectedAmount();
        double target = plugin.getDataManager().getTargetPrice();
        double remaining = target - collected;
        
        if (amount > remaining) {
            return false;
        }
        
        if (!plugin.getEconomyManager().hasEnough(player, amount)) {
            return false;
        }
        
        if (!plugin.getEconomyManager().withdrawPlayer(player, amount)) {
            return false;
        }
        
        plugin.getDataManager().setCollectedAmount(collected + amount);
        plugin.getDataManager().addPaymentToHistory(player.getName(), amount);
        plugin.getDataManager().saveData();
        
        broadcastPayment(player, amount);
        
        if (plugin.getDataManager().getCollectedAmount() >= target) {
            activateTurboDrop();
        }
        
        return true;
    }
    
    public void setTargetPrice(double price) {
        plugin.getDataManager().setTargetPrice(price);
        plugin.getDataManager().saveData();
    }
    
    public long getRemainingTime() {
        if (!plugin.getDataManager().isActive()) {
            return 0;
        }
        
        long endTime = plugin.getDataManager().getEndTime();
        long remaining = endTime - System.currentTimeMillis();
        return Math.max(0, remaining / 1000);
    }
    
    public String getFormattedRemainingTime() {
        long seconds = getRemainingTime();
        return TimeUtils.formatTime(seconds);
    }
    
    public int getAnimationFrame() {
        return animationFrame;
    }
    
    private void broadcastActivation() {
        List<String> messages = plugin.getConfigManager().getMessageList("turbodrop-activated");
        String duration = TimeUtils.formatExactTime(plugin.getConfigManager().getDurationMinutes() * 60);
        
        for (String message : messages) {
            Bukkit.broadcastMessage(message.replace("%duration%", duration));
        }
    }
    
    private void broadcastDeactivation(double collected) {
        List<String> messages = plugin.getConfigManager().getMessageList("turbodrop-deactivated");
        
        for (String message : messages) {
            Bukkit.broadcastMessage(message.replace("%collected%", NumberUtils.formatNumber(collected)));
        }
    }
    
    private void broadcastDisabled() {
        String message = plugin.getConfigManager().getMessage("turbodrop-disabled-broadcast");
        Bukkit.broadcastMessage(message);
    }
    
    private void broadcastPayment(Player player, double amount) {
        String message = plugin.getConfigManager().getMessage("payment-broadcast");
        double collected = plugin.getDataManager().getCollectedAmount();
        double target = plugin.getDataManager().getTargetPrice();
        
        message = message.replace("%player%", player.getName())
                        .replace("%amount%", NumberUtils.formatNumber(amount))
                        .replace("%collected%", NumberUtils.formatNumber(collected))
                        .replace("%target%", NumberUtils.formatNumber(target));
        
        Bukkit.broadcastMessage(message);
    }
    
    private void showBossBar() {
        if (bossBar != null) {
            hideBossBar();
        }

        String styleStr = plugin.getConfig().getString("boss-bar.style", "SOLID");
        BarStyle style;
        try {
            style = BarStyle.valueOf(styleStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            style = BarStyle.SOLID;
        }
        
        bossBar = Bukkit.createBossBar("TurboDrop Aktywny!", BarColor.GREEN, style);

        for (Player player : Bukkit.getOnlinePlayers()) {
            bossBar.addPlayer(player);
        }
        
        bossBar.setVisible(true);

        startBossBarTask();
    }
    
    private void hideBossBar() {
        if (bossBar != null) {
            bossBar.removeAll();
            bossBar.setVisible(false);
            bossBar = null;
        }
        
        if (bossBarTask != null) {
            bossBarTask.cancel();
            bossBarTask = null;
        }
    }
    
    private void startBossBarTask() {
        if (bossBarTask != null) {
            bossBarTask.cancel();
        }
        
        bossBarTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (!plugin.getDataManager().isActive() || bossBar == null) {
                    cancel();
                    return;
                }
                
                long remainingSeconds = getRemainingTime();
                String timeFormatted = TimeUtils.formatTime(remainingSeconds);

                String colorStr = plugin.getConfig().getString("boss-bar.color", "YELLOW");
                BarColor color;
                try {
                    color = BarColor.valueOf(colorStr.toUpperCase());
                } catch (IllegalArgumentException e) {
                    color = BarColor.YELLOW;
                }
                bossBar.setColor(color);

                String title = plugin.getConfig().getString("boss-bar.title", "&6&lTurboDrop Aktywny! &8» &e%time% &8« &ax2 Drop!");
                title = title.replace("%time%", timeFormatted);
                bossBar.setTitle(plugin.getConfigManager().colorize(title));

                long totalDuration = plugin.getConfigManager().getDurationMinutes() * 60;
                double progress = Math.max(0.0, Math.min(1.0, (double) remainingSeconds / totalDuration));
                bossBar.setProgress(progress);

                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (!bossBar.getPlayers().contains(player)) {
                        bossBar.addPlayer(player);
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 20L);
    }
    

    
    public void shutdown() {
        if (countdownTask != null) {
            countdownTask.cancel();
        }
        
        hideBossBar();
    }
}