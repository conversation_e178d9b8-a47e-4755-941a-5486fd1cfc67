package me.darkness.turbodrop.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class TurboDropTabCompleter implements TabCompleter {
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            List<String> options = Arrays.asList("wplac", "info");
            for (String option : options) {
                if (option.toLowerCase().startsWith(args[0].toLowerCase())) {
                    completions.add(option);
                }
            }
        } else if (args.length == 2 && args[0].equalsIgnoreCase("wplac")) {
            List<String> amounts = Arrays.asList("1000", "5000", "10000", "50000", "100000");
            for (String amount : amounts) {
                if (amount.startsWith(args[1])) {
                    completions.add(amount);
                }
            }
        }
        
        return completions;
    }
}