package me.darkness.turbodrop.economy;

import me.darkness.turbodrop.Main;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.entity.Player;
import org.bukkit.plugin.RegisteredServiceProvider;

import static org.bukkit.Bukkit.getServer;

public class EconomyManager {
    
    private final Main plugin;
    private Economy economy;
    
    public EconomyManager(Main plugin) {
        this.plugin = plugin;
    }
    
    public boolean setupEconomy() {
        if (plugin.getServer().getPluginManager().getPlugin("Vault") == null) {
            getServer().getConsoleSender().sendMessage("§8[§4§l777-Economy§8] §cNie znaleziono pluginu §4§nVault§r§c.");
            return false;
        }
        
        RegisteredServiceProvider<Economy> rsp = plugin.getServer().getServicesManager().getRegistration(Economy.class);
        if (rsp == null) {
            getServer().getConsoleSender().sendMessage("§8[§4§l777-Economy§8] §cNie znaleziono pluginu odpowiadajacego za ekonomie! Upewnij się że masz zainstalowany plugin typu Essentials");
            return false;
        }
        
        economy = rsp.getProvider();
        if (economy == null) {
            plugin.getLogger().warning("Failed to get economy provider!");
            return false;
        }

        getServer().getConsoleSender().sendMessage("§8[§6§l777-Economy§8] §fEkonomia: §e§n" + economy.getName());
        return true;
    }
    
    public double getBalance(Player player) {
        if (economy == null) return 0.0;
        return economy.getBalance(player);
    }
    
    public boolean hasEnough(Player player, double amount) {
        if (economy == null) return false;
        return economy.has(player, amount);
    }
    
    public boolean withdrawPlayer(Player player, double amount) {
        if (economy == null) return false;
        return economy.withdrawPlayer(player, amount).transactionSuccess();
    }
    
    public boolean depositPlayer(Player player, double amount) {
        if (economy == null) return false;
        return economy.depositPlayer(player, amount).transactionSuccess();
    }
    
    public String format(double amount) {
        if (economy == null) return String.valueOf(amount);
        return economy.format(amount);
    }
}