package me.darkness.turbodrop.listeners;

import me.darkness.turbodrop.Main;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class ChatListener implements Listener {
    
    private final Main plugin;
    private static final Map<UUID, Boolean> priceSettingMode = new HashMap<>();
    
    public ChatListener(Main plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        
        if (!priceSettingMode.getOrDefault(player.getUniqueId(), false)) {
            return;
        }
        
        event.setCancelled(true);
        
        String message = event.getMessage().trim();
        
        if (message.equalsIgnoreCase("cancel")) {
            priceSettingMode.remove(player.getUniqueId());
            player.sendMessage(plugin.getConfigManager().getMessage("price-set-cancelled"));
            return;
        }
        
        try {
            double price = Double.parseDouble(message);
            if (price <= 0) {
                player.sendMessage(plugin.getConfigManager().getMessage("invalid-price"));
                return;
            }
            
            plugin.getTurboDropManager().setTargetPrice(price);
            priceSettingMode.remove(player.getUniqueId());
            
            String successMessage = plugin.getConfigManager().getMessage("price-set-success");
            successMessage = successMessage.replace("%price%", String.format("%.0f", price));
            player.sendMessage(successMessage);
            
        } catch (NumberFormatException e) {
            player.sendMessage(plugin.getConfigManager().getMessage("invalid-price"));
        }
    }
    
    public static void setPriceSettingMode(UUID playerId, boolean mode) {
        if (mode) {
            priceSettingMode.put(playerId, true);
        } else {
            priceSettingMode.remove(playerId);
        }
    }
}