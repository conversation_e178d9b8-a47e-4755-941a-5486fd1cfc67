package me.darkness.turbodrop.gui;

import me.darkness.turbodrop.Main;
import me.darkness.turbodrop.utils.ItemBuilder;
import me.darkness.turbodrop.utils.NumberUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;

public class GuiManager {
    
    private final Main plugin;
    
    public GuiManager(Main plugin) {
        this.plugin = plugin;
    }
    
    public void openMainGui(Player player) {
        String title = plugin.getConfigManager().getGuiTitle("main");
        int size = plugin.getConfigManager().getGuiSize("main");
        
        Inventory gui = Bukkit.createInventory(null, size, title);
        
        fillBorders(gui);
        
        setStatusItem(gui, player);
        setPaymentItem(gui);
        setBlocksItem(gui);
        setHistoryItem(gui);
        setTimeItem(gui);

        setAdditionalItems(gui);
        
        player.openInventory(gui);
    }
    
    public void openPreviewGui(Player player) {
        String title = plugin.getConfigManager().getGuiTitle("preview");
        int size = plugin.getConfigManager().getGuiSize("preview");
        
        Inventory gui = Bukkit.createInventory(null, size, title);
        
        for (int i = 0; i < size; i++) {
            gui.setItem(i, new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("preview", "border"))
                    .setName(plugin.getConfigManager().getGuiItemName("preview", "border"))
                    .build());
        }
        
        gui.setItem(plugin.getConfigManager().getGuiItemSlot("preview", "ores"),
                new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("preview", "ores"))
                        .setName(plugin.getConfigManager().getGuiItemName("preview", "ores"))
                        .setLore(plugin.getConfigManager().getGuiItemLore("preview", "ores"))
                        .build());
        
        gui.setItem(plugin.getConfigManager().getGuiItemSlot("preview", "deepslate-ores"),
                new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("preview", "deepslate-ores"))
                        .setName(plugin.getConfigManager().getGuiItemName("preview", "deepslate-ores"))
                        .setLore(plugin.getConfigManager().getGuiItemLore("preview", "deepslate-ores"))
                        .build());
        
        gui.setItem(plugin.getConfigManager().getGuiItemSlot("preview", "nether-ores"),
                new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("preview", "nether-ores"))
                        .setName(plugin.getConfigManager().getGuiItemName("preview", "nether-ores"))
                        .setLore(plugin.getConfigManager().getGuiItemLore("preview", "nether-ores"))
                        .build());
        
        gui.setItem(plugin.getConfigManager().getGuiItemSlot("preview", "blocks"),
                new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("preview", "blocks"))
                        .setName(plugin.getConfigManager().getGuiItemName("preview", "blocks"))
                        .setLore(plugin.getConfigManager().getGuiItemLore("preview", "blocks"))
                        .build());
        
        gui.setItem(plugin.getConfigManager().getGuiItemSlot("preview", "other-blocks"),
                new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("preview", "other-blocks"))
                        .setName(plugin.getConfigManager().getGuiItemName("preview", "other-blocks"))
                        .setLore(plugin.getConfigManager().getGuiItemLore("preview", "other-blocks"))
                        .build());
        
        gui.setItem(plugin.getConfigManager().getGuiItemSlot("preview", "back"),
                new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("preview", "back"))
                        .setName(plugin.getConfigManager().getGuiItemName("preview", "back"))
                        .setLore(plugin.getConfigManager().getGuiItemLore("preview", "back"))
                        .build());
        
        player.openInventory(gui);
    }
    
    private void fillBorders(Inventory gui) {
        ItemStack borderItem = new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("main", "border"))
                .setName(plugin.getConfigManager().getGuiItemName("main", "border"))
                .build();
        
        for (int i = 0; i < gui.getSize(); i++) {
            gui.setItem(i, borderItem);
        }
        
        ItemStack accentBorder = new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("main", "accent-border"))
                .setName(plugin.getConfigManager().getGuiItemName("main", "accent-border"))
                .build();
        
        List<Integer> accentSlots = plugin.getConfigManager().getGuiItemSlots("main", "accent-border");
        for (int slot : accentSlots) {
            gui.setItem(slot, accentBorder);
        }
    }
    
    private void setStatusItem(Inventory gui, Player player) {
        boolean isActive = plugin.getDataManager().isActive();
        Material material;
        String status;
        
        if (isActive) {
            material = plugin.getConfigManager().getGuiItemMaterial("main", "status", "active");
            status = plugin.getConfigManager().getStatusText("main", "status", "active");
        } else {
            material = plugin.getConfigManager().getGuiItemMaterial("main", "status", "inactive");
            status = plugin.getConfigManager().getStatusText("main", "status", "inactive");
        }
        
        double collected = plugin.getDataManager().getCollectedAmount();
        double target = plugin.getDataManager().getTargetPrice();
        String progressBar = createProgressBar(collected, target);
        
        List<String> lore = new ArrayList<>(plugin.getConfigManager().getGuiItemLore("main", "status"));
        for (int i = 0; i < lore.size(); i++) {
            lore.set(i, lore.get(i)
                    .replace("%status%", status)
                    .replace("%collected%", NumberUtils.formatNumber(collected))
                    .replace("%target%", NumberUtils.formatNumber(target))
                    .replace("%progress%", progressBar));
        }
        
        gui.setItem(plugin.getConfigManager().getGuiItemSlot("main", "status"),
                new ItemBuilder(material)
                        .setName(plugin.getConfigManager().getGuiItemName("main", "status"))
                        .setLore(lore)
                        .build());
    }
    
    private void setPaymentItem(Inventory gui) {
        boolean isActive = plugin.getDataManager().isActive();
        
        if (isActive) {
            gui.setItem(plugin.getConfigManager().getGuiItemSlot("main", "payment"),
                    new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("main", "payment", "active"))
                            .setName(plugin.getConfigManager().getGuiItemName("main", "payment", "active"))
                            .setLore(plugin.getConfigManager().getGuiItemLore("main", "payment", "active"))
                            .build());
        } else {
            gui.setItem(plugin.getConfigManager().getGuiItemSlot("main", "payment"),
                    new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("main", "payment", "inactive"))
                            .setName(plugin.getConfigManager().getGuiItemName("main", "payment", "inactive"))
                            .setLore(plugin.getConfigManager().getGuiItemLore("main", "payment", "inactive"))
                            .build());
        }
    }
    
    private void setBlocksItem(Inventory gui) {
        gui.setItem(plugin.getConfigManager().getGuiItemSlot("main", "blocks"),
                new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("main", "blocks"))
                        .setName(plugin.getConfigManager().getGuiItemName("main", "blocks"))
                        .setLore(plugin.getConfigManager().getGuiItemLore("main", "blocks"))
                        .build());
    }
    
    private void setHistoryItem(Inventory gui) {
        List<String> history = plugin.getDataManager().getPaymentHistory();
        List<String> lore = new ArrayList<>();

        List<String> baseLore = plugin.getConfigManager().getGuiItemLore("main", "history");
        for (String line : baseLore) {
            if (line.contains("%history%")) {
                if (history.isEmpty()) {
                    lore.add(line.replace("%history%", "&7Brak wpłat"));
                } else {
                    for (String entry : history) {
                        lore.add(entry);
                    }
                }
            } else {
                lore.add(line);
            }
        }
        
        gui.setItem(plugin.getConfigManager().getGuiItemSlot("main", "history"),
                new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("main", "history"))
                        .setName(plugin.getConfigManager().getGuiItemName("main", "history"))
                        .setLore(lore)
                        .build());
    }
    
    private void setTimeItem(Inventory gui) {
        boolean isActive = plugin.getDataManager().isActive();
        
        if (isActive) {
            String timeRemaining = plugin.getTurboDropManager().getFormattedRemainingTime();
            List<String> lore = new ArrayList<>(plugin.getConfigManager().getGuiItemLore("main", "time", "active"));
            for (int i = 0; i < lore.size(); i++) {
                lore.set(i, lore.get(i).replace("%time%", timeRemaining));
            }
            
            gui.setItem(plugin.getConfigManager().getGuiItemSlot("main", "time"),
                    new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("main", "time"))
                            .setName(plugin.getConfigManager().getGuiItemName("main", "time"))
                            .setLore(lore)
                            .build());
        } else {
            gui.setItem(plugin.getConfigManager().getGuiItemSlot("main", "time"),
                    new ItemBuilder(plugin.getConfigManager().getGuiItemMaterial("main", "time"))
                            .setName(plugin.getConfigManager().getGuiItemName("main", "time"))
                            .setLore(plugin.getConfigManager().getGuiItemLore("main", "time", "inactive"))
                            .build());
        }
    }
    

    
    private void setAdditionalItems(Inventory gui) {
        List<String> knownItems = List.of("status", "payment", "blocks", "history", "time", "border", "accent-border");

        if (plugin.getConfig().getConfigurationSection("gui.main.items") != null) {
            for (String itemKey : plugin.getConfig().getConfigurationSection("gui.main.items").getKeys(false)) {
                if (knownItems.contains(itemKey)) {
                    continue;
                }

                if (plugin.getConfig().contains("gui.main.items." + itemKey + ".slot") &&
                    plugin.getConfig().contains("gui.main.items." + itemKey + ".name") &&
                    plugin.getConfig().contains("gui.main.items." + itemKey + ".material")) {
                    
                    int slot = plugin.getConfigManager().getGuiItemSlot("main", itemKey);
                    String name = plugin.getConfigManager().getGuiItemName("main", itemKey);
                    Material material = plugin.getConfigManager().getGuiItemMaterial("main", itemKey);
                    List<String> lore = plugin.getConfigManager().getGuiItemLore("main", itemKey);
                    
                    gui.setItem(slot, new ItemBuilder(material)
                            .setName(name)
                            .setLore(lore)
                            .build());
                }
            }
        }
    }
    
    private String createProgressBar(double current, double max) {
        int length = plugin.getConfigManager().getProgressBarLength();
        String filledChar = plugin.getConfigManager().getProgressBarFilledChar();
        String emptyChar = plugin.getConfigManager().getProgressBarEmptyChar();
        String format = plugin.getConfigManager().getProgressBarFormat();
        
        int filled = (int) ((current / max) * length);
        
        StringBuilder bar = new StringBuilder();
        for (int i = 0; i < length; i++) {
            if (i < filled) {
                bar.append(filledChar);
            } else {
                bar.append(emptyChar);
            }
        }
        
        return format.replace("%bar%", bar.toString());
    }
}