package me.darkness.turbodrop.listeners;

import me.darkness.turbodrop.Main;
import me.darkness.turbodrop.utils.ColorUtils;
import me.darkness.turbodrop.utils.NumberUtils;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;

import java.util.List;

public class InventoryListener implements Listener {
    
    private final Main plugin;
    
    public InventoryListener(Main plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();
        
        String mainTitle = plugin.getConfigManager().getGuiTitle("main");
        String previewTitle = plugin.getConfigManager().getGuiTitle("preview");

        boolean isMainGui = title.equals(mainTitle) && 
                           event.getInventory().getSize() == plugin.getConfigManager().getGuiSize("main") &&
                           event.getClickedInventory() != null &&
                           event.getClickedInventory().equals(event.getView().getTopInventory());
                           
        boolean isPreviewGui = title.equals(previewTitle) && 
                              event.getInventory().getSize() == plugin.getConfigManager().getGuiSize("preview") &&
                              event.getClickedInventory() != null &&
                              event.getClickedInventory().equals(event.getView().getTopInventory());
        
        if (!isMainGui && !isPreviewGui) {
            return;
        }
        
        event.setCancelled(true);
        
        if (isMainGui) {
            handleMainGuiClick(player, event.getSlot());
        } else if (isPreviewGui) {
            handlePreviewGuiClick(player, event.getSlot());
        }
    }
    
    private void handleMainGuiClick(Player player, int slot) {
        if (slot == plugin.getConfigManager().getGuiItemSlot("main", "status")) {
            player.closeInventory();
            showDetailedInfo(player);
        } else if (slot == plugin.getConfigManager().getGuiItemSlot("main", "payment")) {
            if (!plugin.getDataManager().isActive()) {
                player.closeInventory();
                player.sendMessage(plugin.getConfigManager().getMessage("use-command-to-pay"));
            }
        } else if (slot == plugin.getConfigManager().getGuiItemSlot("main", "blocks")) {
            plugin.getGuiManager().openPreviewGui(player);
        } else if (slot == plugin.getConfigManager().getGuiItemSlot("main", "history")) {
            plugin.getGuiManager().openMainGui(player);
        }
    }
    
    private void showDetailedInfo(Player player) {
        List<String> messages = plugin.getConfigManager().getMessageList("info");
        double collected = plugin.getDataManager().getCollectedAmount();
        double target = plugin.getDataManager().getTargetPrice();
        
        String status;
        String time;
        
        if (plugin.getDataManager().isActive()) {
            status = "&aAktywny";
            time = plugin.getTurboDropManager().getFormattedRemainingTime();
        } else {
            status = "&cNieaktywny";
            time = "&cOFF";
        }
        
        for (String message : messages) {
            message = message.replace("%collected%", NumberUtils.formatNumber(collected))
                            .replace("%target%", NumberUtils.formatNumber(target))
                            .replace("%status%", status)
                            .replace("%time%", time);
            player.sendMessage(plugin.getConfigManager().colorize(message));
        }
    }
    
    private void handlePreviewGuiClick(Player player, int slot) {
        if (slot == plugin.getConfigManager().getGuiItemSlot("preview", "back")) {
            plugin.getGuiManager().openMainGui(player);
        }
    }
}