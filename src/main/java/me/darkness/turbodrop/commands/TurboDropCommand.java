package me.darkness.turbodrop.commands;

import me.darkness.turbodrop.Main;
import me.darkness.turbodrop.utils.NumberUtils;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.List;

public class TurboDropCommand implements CommandExecutor {
    
    private final Main plugin;
    
    public TurboDropCommand(Main plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("This command can only be used by players!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            plugin.getGuiManager().openMainGui(player);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "wplac":
                handlePayment(player, args);
                break;
            case "info":
                handleInfo(player);
                break;
            default:
                plugin.getGuiManager().openMainGui(player);
                break;
        }
        
        return true;
    }
    
    private void handlePayment(Player player, String[] args) {
        if (plugin.getDataManager().isActive()) {
            player.sendMessage(plugin.getConfigManager().getMessage("cannot-pay-active"));
            return;
        }
        
        if (args.length < 2) {
            player.sendMessage(plugin.getConfigManager().getMessage("specify-amount"));
            return;
        }
        
        double amount;
        try {
            amount = Double.parseDouble(args[1]);
        } catch (NumberFormatException e) {
            player.sendMessage(plugin.getConfigManager().getMessage("specify-amount"));
            return;
        }
        
        if (amount <= 0) {
            player.sendMessage(plugin.getConfigManager().getMessage("amount-must-be-positive"));
            return;
        }
        
        double collected = plugin.getDataManager().getCollectedAmount();
        double target = plugin.getDataManager().getTargetPrice();
        double remaining = target - collected;
        
        if (amount > remaining) {
            String message = plugin.getConfigManager().getMessage("amount-too-high");
            message = message.replace("%amount%", String.format("%.0f", remaining));
            player.sendMessage(message);
            return;
        }
        
        if (!plugin.getEconomyManager().hasEnough(player, amount)) {
            player.sendMessage(plugin.getConfigManager().getMessage("insufficient-funds"));
            return;
        }
        
        if (plugin.getTurboDropManager().makePayment(player, amount)) {
            String message = plugin.getConfigManager().getMessage("payment-success");
            message = message.replace("%amount%", String.format("%.0f", amount));
            player.sendMessage(message);
        }
    }
    
    private void handleInfo(Player player) {
        List<String> messages = plugin.getConfigManager().getMessageList("info");
        double collected = plugin.getDataManager().getCollectedAmount();
        double target = plugin.getDataManager().getTargetPrice();
        
        String status;
        String time;
        
        if (plugin.getDataManager().isActive()) {
            status = "&aAktywny";
            time = plugin.getTurboDropManager().getFormattedRemainingTime();
        } else {
            status = "&cNieaktywny";
            time = "&cOFF";
        }
        
        for (String message : messages) {
            message = message.replace("%collected%", NumberUtils.formatNumber(collected))
                            .replace("%target%", NumberUtils.formatNumber(target))
                            .replace("%status%", status)
                            .replace("%time%", time);
            player.sendMessage(plugin.getConfigManager().colorize(message));
        }
    }
    

}