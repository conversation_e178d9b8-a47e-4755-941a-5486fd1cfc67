package me.darkness.turbodrop.utils;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

public class NumberUtils {
    
    private static final NumberFormat formatter = NumberFormat.getNumberInstance(Locale.US);
    private static final DecimalFormat decimalFormatter = new DecimalFormat("#,##0.#");
    
    static {
        formatter.setGroupingUsed(true);
        formatter.setMaximumFractionDigits(0);
        
        // Ustawienia dla formattera z przecinkami
        decimalFormatter.setDecimalSeparatorAlwaysShown(false);
        decimalFormatter.setGroupingUsed(false);
    }

    public static String formatNumber(double number) {
        return formatter.format(number);
    }

    public static String formatNumber(long number) {
        return formatter.format(number);
    }

    public static String formatNumber(int number) {
        return formatter.format(number);
    }
    
    public static String formatDecimal(double number) {
        return decimalFormatter.format(number).replace('.', ',');
    }
}